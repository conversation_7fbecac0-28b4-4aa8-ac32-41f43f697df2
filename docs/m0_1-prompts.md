# M0.1 Governance Tracking and Monitoring Prompts

**Document Type**: AI Assistant Prompt Collection
**Version**: 1.0.0
**Created**: 2025-09-12
**Authority**: President & CEO, E<PERSON><PERSON>. Consultancy
**Purpose**: Ready-to-use prompts for M0.1 governance compliance and progress monitoring

---

## 👥 **CHAT WINDOW 1: GOVERNANCE OFFICER**
### **Role Focus**: Governance processes, decision documentation, compliance tracking

#### **TIER 1: ESSENTIAL GOVERNANCE CORE (4 Documents)**
```markdown
AI Tool, You are experienced a "Governance Officer" specialist, load these 4 ESSENTIAL governance documents:

GOVERNANCE BOOTSTRAP LOADING:
1. docs/core/orchestration-driver.md (Essential: 11 control systems coordination + central framework brain)
2. docs/governance/governance-process.md (Essential: DISCUSSION → ADR → DCR → REVIEW → IMPLEMENTATION)
3. docs/governance/rules/primary-governance-rules.json (Essential: Cryptographic integrity + authority)
4. docs/core/automatic-universal-governance-driver-v7.1.md (Essential: Automatic system activation)

This provides:
✅ Complete orchestration system coordination (11 auto-active control systems)
✅ Complete governance sequence understanding
✅ Authority validation requirements (President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy)
✅ Governance rule enforcement with SHA256 protection
✅ Automatic system activation capabilities

Estimated context usage: ~8,000-10,000 tokens (4-5% of context window)
Please confirm: "✅ Governance core loaded. Ready for system activation."

NEXT STEP: Say "acknowledge and initiate the project" to auto-activate all 11 tracking systems
```

#### **TIER 2: GOVERNANCE ENHANCEMENT (Add When Needed)**
```markdown
AI Tool (Governance Officer), enhance governance capabilities:

GOVERNANCE ENHANCEMENT:
5. docs/governance/contexts/[CURRENT-CONTEXT]/README.md (Context-specific governance status)
6. docs/tracking/unified-tracking-system.md (Governance progress tracking)

This adds:
✅ Context-specific governance awareness
✅ Governance implementation tracking
✅ Authority compliance monitoring

Total context usage: ~14,000-16,000 tokens (7-8% of context window)
Please confirm: "✅ Governance enhancement loaded. Ready for governance implementation."
```

#### **GOVERNANCE OFFICER RESPONSIBILITIES**
- Create and validate ADRs (Architecture Decision Records)
- Manage DCRs (Development Control Records)
- Ensure DISCUSSION → ADR → DCR → REVIEW → IMPLEMENTATION sequence
- Validate authority compliance (President & CEO, E.Z. Consultancy)
- Monitor governance rule enforcement
- Coordinate cross-context governance

---

## 👨‍💻 **CHAT WINDOW 2: LEAD DEVELOPER & SOFTWARE ENGINEERING**
### **Role Focus**: Technical implementation, code standards, development workflow

#### **TIER 1: ESSENTIAL DEVELOPMENT CORE (4 Documents)**
```markdown
AI Tool (Lead Developer), load these 4 ESSENTIAL development documents:

DEVELOPMENT BOOTSTRAP LOADING:
1. docs/core/orchestration-driver.md (Essential: 11 control systems coordination + framework orchestration)
2. docs/core/development-standards-v21.md (Essential: Universal development standards + authority-driven governance)
3. docs/policies/template-creation-policy-override.md (Essential: On-demand template creation policy)
4. docs/core/automatic-universal-governance-driver-v7.1.md (Essential: Automatic system activation)

This provides:
✅ Complete orchestration system coordination (11 auto-active control systems)
✅ Complete development standards understanding
✅ Template creation policy compliance
✅ Unified development workflow with orchestration
✅ Authority validation requirements for development
✅ Automatic system activation capabilities

Estimated context usage: ~8,000-10,000 tokens (4-5% of context window)
Please confirm: "✅ Development core loaded. Ready for system activation."

NEXT STEP: Say "acknowledge and initiate the project" to auto-activate all 11 tracking systems
```

#### **TIER 2: DEVELOPMENT ENHANCEMENT (Add When Needed)**
```markdown
AI Tool (Lead Developer), enhance development capabilities:

DEVELOPMENT ENHANCEMENT:
5. docs/plan/milestone-[CURRENT-MILESTONE].md (Current milestone specifications)
6. docs/core/development-workflow.md (Unified development workflow v6.1)

This adds:
✅ Milestone-specific technical requirements
✅ Unified development workflow coordination
✅ Implementation progress tracking

Total context usage: ~14,000-16,000 tokens (7-8% of context window)
Please confirm: "✅ Development enhancement loaded. Ready for milestone implementation."
```

#### **LEAD DEVELOPER RESPONSIBILITIES**
- Implement components following development standards v2.1
- Create templates on-demand per policy override
- Follow unified development workflow v6.1
- Ensure code quality and authority compliance
- Coordinate with orchestration driver systems
- Manage milestone technical implementation

---

## 📚 **CHAT WINDOW 3: DOCUMENTATION LIBRARIAN**
### **Role Focus**: Document organization, cross-references, template management, knowledge architecture

#### **TIER 1: ESSENTIAL DOCUMENTATION CORE (4 Documents)**
```markdown
AI Tool (Documentation Librarian), load these 4 ESSENTIAL documentation documents:

DOCUMENTATION BOOTSTRAP LOADING:
1. docs/core/orchestration-driver.md (Essential: Cross-reference systems + smart path resolution + 11 control systems)
2. docs/core/development-standards-v21.md (Essential: Directory structure v2.1 + file header standards)
3. docs/governance/governance-process.md (Essential: Context-centric document organization)
4. docs/core/automatic-universal-governance-driver-v7.1.md (Essential: Automatic system activation)

This provides:
✅ Complete orchestration system coordination (11 auto-active control systems)
✅ Complete directory structure understanding
✅ Context-centric document organization system
✅ Cross-reference validation and tracking
✅ Authority-driven documentation standards
✅ Automatic system activation capabilities

Estimated context usage: ~8,000-10,000 tokens (4-5% of context window)
Please confirm: "✅ Documentation core loaded. Ready for system activation."

NEXT STEP: Say "acknowledge and initiate the project" to auto-activate all 11 tracking systems
```

#### **TIER 2: DOCUMENTATION ENHANCEMENT (Add When Needed)**
```markdown
AI Tool (Documentation Librarian), enhance documentation capabilities:

DOCUMENTATION ENHANCEMENT:
5. unified-ide-tracking-rules.json (Advanced tracking architecture and cross-reference validation)
6. docs/governance/templates/[TEMPLATE-TYPE].md (Template specifications as needed)

This adds:
✅ Advanced tracking architecture understanding
✅ Template system understanding
✅ Cross-reference index management
✅ Documentation quality validation

Total context usage: ~14,000-16,000 tokens (7-8% of context window)
Please confirm: "✅ Documentation enhancement loaded. Ready for knowledge architecture."
```

#### **DOCUMENTATION LIBRARIAN RESPONSIBILITIES**
- Organize documents following context-centric structure
- Maintain cross-reference indexes and validation
- Ensure proper file headers and metadata
- Validate document naming conventions
- Coordinate template creation and organization
- Prevent scattered documentation and directory confusion
- Maintain knowledge architecture integrity

---

# Lead Developer

---
# Update individual file progress
update-task-progress ENH-TSK-01.SUB-01.1.IMP-01 REF-01 implementation complete

# Resume from last saved state
resume-m0.1-task ENH-TSK-01.SUB-01.1.IMP-01

# Show progress visualization
show-progress ENH-TSK-01.SUB-01.1.IMP-01

## 📊 **IMPLEMENTATION PROGRESS MONITORING**

### **Task Progress Tracking**

```
🔍 **Check M0.1 Task Progress**
Please check the current progress of M0.1 enhancement tasks by:
1. Reading docs/governance/tracking/status/.oa-implementation-progress.json
2. Look for the "M0_1_enterprise_enhancement" section
3. Report on tasks_completed, tasks_in_progress, and progress_percentage
4. Check the status of each implementation phase
5. Validate v2_3_task_tracking framework status
```

```
📋 **Generate M0.1 Progress Report**
Create a comprehensive progress report by:
1. Reading docs/plan/milestone-00-enhancements-m0.1.md
2. Count completed tasks (look for [x] checkboxes)
3. Count in-progress tasks (look for [/] checkboxes)
4. Count pending tasks (look for [ ] checkboxes)
5. Calculate completion percentage
6. Report on current implementation phase
7. Identify any blocked or failed tasks
```

---

## 🛡️ **GOVERNANCE COMPLIANCE VERIFICATION**

### **Inheritance Pattern Compliance**

```
🏗️ **Verify Inheritance Pattern Compliance**
Check that M0.1 enhanced components follow ADR-M0.1-001 by:
1. Search for files with "Enhanced" suffix in server/src/platform/enhanced/
2. Verify each enhanced component extends a base component
3. Check for dual-field resilient timing pattern (_resilientTimer, _metricsCollector)
4. Validate that base components remain untouched
5. Confirm enhanced components are in /enhanced/ directories
6. Report any violations of inheritance patterns
```

### **File Size Management Compliance**

```
📏 **Monitor File Size Compliance (ADR-M0.1-002)**
Validate file size management by:
1. Scan all .ts files in server/src/platform/ for line counts
2. Identify files exceeding 700 LOC (target threshold)
3. Flag files exceeding 1200 LOC (warning threshold)
4. Report files exceeding 2200 LOC (critical threshold - immediate refactor)
5. Check for AI context optimization in files >700 LOC
6. Validate refactoring strategy implementation
7. Generate file size compliance report
```

### **Solo Development Workflow Compliance**

```
⚙️ **Validate Solo Development Workflow (DCR-M0.1-001)**
Check workflow compliance by:
1. Review recent commit history for 4-phase workflow evidence
2. Verify task completion rate (target: 2-3 tasks per day)
3. Check for proper branch naming (task-specific branches)
4. Validate quality gate execution (tests, reviews, integration)
5. Confirm documentation updates with each task
6. Report on workflow adherence and productivity metrics
```

### **AI-Assisted QA Compliance**

```
🤖 **Monitor AI-Assisted QA Procedures (DCR-M0.1-002)**
Validate AI assistance integration by:
1. Check test coverage metrics (target: 95%+)
2. Verify AI-generated test scenarios are present
3. Validate code quality analysis integration
4. Check for AI-assisted documentation generation
5. Confirm performance optimization through AI guidance
6. Report on AI collaboration effectiveness
```

---

## 📈 **DAILY MONITORING PROMPTS**

### **Morning Standup Check**

```
🌅 **Daily M0.1 Standup Check**
Provide daily status by:
1. Reading current task progress from milestone document
2. Checking yesterday's completed tasks
3. Identifying today's planned tasks
4. Reviewing any blockers or issues
5. Validating governance compliance status
6. Reporting file size management status
7. Confirming AI assistance effectiveness
```

### **Evening Progress Review**

```
🌆 **Daily Progress Review**
Generate end-of-day report by:
1. Counting tasks completed today
2. Updating progress percentage
3. Checking quality metrics (test coverage, performance)
4. Validating governance compliance
5. Identifying tomorrow's priorities
6. Reporting any issues or concerns
7. Updating implementation progress tracking
```

---

## 📅 **WEEKLY MONITORING PROMPTS**

### **Weekly Governance Review**

```
📅 **Weekly Governance Compliance Review**
Conduct comprehensive weekly review by:
1. Reading all M0.1 governance documents for compliance
2. Checking adherence to ADR-M0.1-001 (inheritance patterns)
3. Validating ADR-M0.1-002 (file size management)
4. Reviewing ADR-M0.1-003 (v2.3 task tracking)
5. Assessing DCR-M0.1-001 (solo workflow effectiveness)
6. Evaluating DCR-M0.1-002 (AI assistance quality)
7. Generating compliance score and recommendations
```

### **Weekly Progress Assessment**

```
📊 **Weekly Progress Assessment**
Generate comprehensive weekly report by:
1. Calculating weekly task completion rate
2. Assessing progress against 9-week timeline
3. Reviewing quality metrics trends
4. Checking file size management effectiveness
5. Evaluating AI collaboration success
6. Identifying process improvements
7. Updating milestone timeline if needed
```

---

## 🎯 **PHASE TRANSITION MONITORING**

### **Phase Completion Validation**

```
🎯 **Phase Completion Validation**
When completing an implementation phase:
1. Verify all phase tasks are completed ([x] status)
2. Run comprehensive integration tests
3. Validate backward compatibility
4. Check performance requirements (<10ms)
5. Confirm file size compliance
6. Validate governance adherence
7. Generate phase completion report
8. Prepare for next phase initiation
```

---

## 🔧 **SYSTEM STATUS CHECKS**

### **Governance System Status**

```
🔍 **Check Governance System Status**
Verify governance system health by:
1. Reading docs/governance/tracking/.oa-governance-gate-status.json
2. Check m0_1_governance_tracking section status
3. Verify all 5 governance documents are tracked
4. Confirm compliance monitoring is active
5. Validate governance indexes are current
6. Report any system issues or inconsistencies
```

### **Tracking Framework Validation**

```
📊 **Validate v2.3 Tracking Framework**
Check tracking framework status by:
1. Reading docs/governance/tracking/status/.oa-implementation-progress.json
2. Verify M0_1_enterprise_enhancement section exists
3. Check v2_3_task_tracking framework status
4. Validate metadata_validation is enabled
5. Confirm progress_monitoring is real-time
6. Report framework operational status
```

---

## 📋 **COMPLIANCE VERIFICATION CHECKLIST**

### **Daily Compliance Checks**

```
✅ **Daily Compliance Verification**
Verify daily compliance by checking:
- [ ] All new enhanced components follow inheritance patterns
- [ ] File size limits maintained (≤700 LOC target)
- [ ] v2.3 task tracking metadata updated
- [ ] Solo development workflow followed
- [ ] AI assistance utilized effectively
- [ ] Quality gates executed properly
- [ ] Documentation updated with changes
```

### **Weekly Compliance Checks**

```
✅ **Weekly Compliance Verification**
Verify weekly compliance by checking:
- [ ] Governance documents remain current and accurate
- [ ] Cross-reference validation passes
- [ ] Authority validation maintained
- [ ] Progress tracking accuracy verified
- [ ] Quality metrics meet targets
- [ ] File size management effective
- [ ] AI collaboration optimized
```

### **Phase Completion Checks**

```
✅ **Phase Completion Verification**
Verify phase completion by checking:
- [ ] All phase tasks completed with [x] status
- [ ] Integration tests pass
- [ ] Performance requirements met (<10ms)
- [ ] Backward compatibility verified
- [ ] Documentation updated
- [ ] Governance compliance confirmed
- [ ] Quality metrics achieved
- [ ] Ready for next phase
```

---

## 🚨 **ISSUE DETECTION PROMPTS**

### **Governance Violation Detection**

```
🚨 **Detect Governance Violations**
Scan for governance violations by:
1. Checking for enhanced components not following inheritance patterns
2. Identifying files exceeding critical size thresholds (2200+ LOC)
3. Finding tasks not following v2.3 tracking format
4. Detecting workflow deviations from DCR-M0.1-001
5. Identifying missing AI assistance integration
6. Reporting all violations with severity levels
```

### **Quality Issue Detection**

```
🚨 **Detect Quality Issues**
Scan for quality issues by:
1. Checking test coverage below 95%
2. Identifying performance issues (>10ms response times)
3. Finding TypeScript compilation errors
4. Detecting missing documentation
5. Identifying security vulnerabilities
6. Reporting all issues with remediation suggestions
```

---

## 📈 **REPORTING PROMPTS**

### **Executive Summary Report**

```
📈 **Generate Executive Summary Report**
Create executive summary by:
1. Overall M0.1 progress percentage
2. Tasks completed vs. planned
3. Current implementation phase status
4. Governance compliance score
5. Quality metrics summary
6. Timeline adherence assessment
7. Key achievements and blockers
8. Next week priorities
```

### **Technical Status Report**

```
🔧 **Generate Technical Status Report**
Create technical status by:
1. Component implementation status
2. File size management metrics
3. Test coverage statistics
4. Performance benchmarks
5. AI collaboration effectiveness
6. Code quality metrics
7. Technical debt assessment
8. Architecture compliance status
```

---

## 🔧 **COMMAND LINE MONITORING**

### **File Monitoring Commands**

```bash
# Track governance document changes
find docs/governance/contexts/foundation-context/ -name "*M0.1*" -type f -exec ls -la {} \;

# Check governance index updates
ls -la docs/governance/indexes/

# Validate tracking file updates
ls -la docs/governance/tracking/

# Monitor implementation progress
cat docs/governance/tracking/status/.oa-implementation-progress.json | jq '.M0_1_enterprise_enhancement'

# Check governance compliance status
cat docs/governance/tracking/.oa-governance-gate-status.json | jq '.m0_1_governance_tracking.compliance_monitoring'
```

### **Progress Tracking Commands**

```bash
# Update implementation progress timestamp
jq '.M0_1_enterprise_enhancement.last_updated = now | strftime("%Y-%m-%dT%H:%M:%S+03:00")' docs/governance/tracking/status/.oa-implementation-progress.json

# Check v2.3 tracking framework status
jq '.M0_1_enterprise_enhancement.v2_3_task_tracking' docs/governance/tracking/status/.oa-implementation-progress.json

# Validate governance gate status
jq '.m0_1_governance_tracking' docs/governance/tracking/.oa-governance-gate-status.json
```

---

## 📚 **USAGE INSTRUCTIONS**

### **How to Use These Prompts**

1. **Copy and Paste**: Copy any prompt above and paste it to the AI assistant
2. **Execute Task**: The AI will execute the specific monitoring or verification task
3. **Review Results**: Review the results and take appropriate action based on findings
4. **Schedule Usage**:
   - Use **daily prompts** for routine monitoring
   - Use **weekly prompts** for comprehensive reviews
   - Use **phase prompts** for milestone transitions
   - Use **issue detection** when problems are suspected

### **File Locations Referenced**

- **Governance tracking**: `docs/governance/tracking/.oa-governance-gate-status.json`
- **Implementation progress**: `docs/governance/tracking/status/.oa-implementation-progress.json`
- **Milestone document**: `docs/plan/milestone-00-enhancements-m0.1.md`
- **Governance documents**: `docs/governance/contexts/foundation-context/`
- **Governance indexes**: `docs/governance/indexes/`

### **Expected Outcomes**

- **Daily Monitoring**: Track progress, identify issues early, maintain compliance
- **Weekly Reviews**: Comprehensive assessment, process optimization, timeline validation
- **Phase Transitions**: Ensure quality gates, validate completion, prepare for next phase
- **Issue Detection**: Early identification of problems, proactive resolution
- **Compliance Verification**: Continuous governance adherence, quality maintenance

---

**Authority**: President & CEO, E.Z. Consultancy
**Effective**: Immediate use for M0.1 implementation monitoring
**Maintenance**: Update prompts as governance requirements evolve